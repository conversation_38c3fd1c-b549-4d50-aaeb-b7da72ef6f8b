'use client';
import { Container } from 'react-bootstrap';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';
import styles from './BlogListing.module.css';
import Link from 'next/link';
import Button from '@components/Button';
import Heading from '@components/Heading';
import useMediaQueryState from '@hooks/useMediaQueryState';
import breakpoints from '@styles/breakpoints.module.css';
import ReactPaginate from 'react-paginate';
import { formatPublishDate } from '@utils/dateFormatter';

export default function BlogListing({
  filterdata,
  boxData,
  dropDownServicesData,
  dropDownIndustryData,
}: any) {
  // Manage which dropdown is open (null, 'services', or 'industry')
  const [openDropdown, setOpenDropdown] = useState<
    'services' | 'industry' | null
  >(null);

  // States to store selected services and industries
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([]);

  ///// pagination ///////////

  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = 6;

  const handlePageChange = selectedPage => {
    setCurrentPage(selectedPage.selected); // ReactPaginate gives selected as object {selected: pageNumber}
  };

  const filteredData =
    boxData?.filter(data => {
      const serviceTitles = data?.attributes?.global_services?.data?.map(
        service => service?.attributes?.service_title,
      );
      const industryTitles = data?.attributes?.global_industries?.data?.map(
        industry => industry?.attributes?.industry_title,
      );

      const hasMatchingService =
        selectedServices.length === 0 ||
        selectedServices.some(selected => serviceTitles?.includes(selected));

      const hasMatchingIndustry =
        selectedIndustries.length === 0 ||
        selectedIndustries.some(selected => industryTitles?.includes(selected));

      return hasMatchingService && hasMatchingIndustry;
    }) || [];
  const pageCount = Math.ceil(filteredData?.length / itemsPerPage);
  const indexOfLastItem = (currentPage + 1) * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const paginatedData = filteredData?.slice(indexOfFirstItem, indexOfLastItem); // Paginate the filtered data

  // Reset currentPage to 0 when filter conditions change
  useEffect(() => {
    setCurrentPage(0);
  }, [selectedServices, selectedIndustries]);

  ////////////////////////////////////////////////////

  const combinedSelectedValues = [...selectedServices, ...selectedIndustries];

  // Function to remove selected value
  const removeSelectedValue = value => {
    setSelectedServices(selectedServices.filter(service => service !== value));
    setSelectedIndustries(
      selectedIndustries.filter(industry => industry !== value),
    );
  };

  const toggleDropdownServices = () => {
    setOpenDropdown(prev => (prev === 'services' ? null : 'services'));
  };

  const toggleDropdownIndustry = () => {
    setOpenDropdown(prev => (prev === 'industry' ? null : 'industry'));
  };

  // Handler for selecting/unselecting services
  const handleServiceChange = (serviceTitle: string) => {
    setSelectedServices(prevSelectedServices => {
      if (prevSelectedServices.includes(serviceTitle)) {
        return prevSelectedServices.filter(service => service !== serviceTitle);
      } else {
        return [...prevSelectedServices, serviceTitle];
      }
    });
  };

  // Handler for selecting/unselecting industries
  const handleIndustryChange = (industryTitle: string) => {
    setSelectedIndustries(prevSelectedIndustries => {
      if (prevSelectedIndustries.includes(industryTitle)) {
        return prevSelectedIndustries.filter(
          industry => industry !== industryTitle,
        );
      } else {
        return [...prevSelectedIndustries, industryTitle];
      }
    });
  };

  const isTablet = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-md']})`,
  });

  // State to control the visibility of the filter container
  const [isFilterVisible, setIsFilterVisible] = useState(false);

  // Function to toggle visibility
  const toggleFilterVisibility = () => {
    setIsFilterVisible(!isFilterVisible); // Toggle visibility
  };
  // Dropdown refs
  const servicesDropdownRef = useRef<HTMLDivElement>(null);
  const industryDropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        servicesDropdownRef.current &&
        !servicesDropdownRef.current.contains(event.target as Node) &&
        industryDropdownRef.current &&
        !industryDropdownRef.current.contains(event.target as Node)
      ) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  return (
    <>
      {!isTablet ? (
        <>
          <Container fluid className={styles.filter_container}>
            <Heading
              headingType="h2"
              title={filterdata?.title}
              className={styles.filter_title}
            />
            <div className={styles.filter_tabs}>
              <div className={styles.dropdown_tabs}>
                {/* Services Dropdown */}
                <div
                  ref={servicesDropdownRef}
                  className={styles.dropdown_tabs_services}
                >
                  <div
                    className={styles.dropdown_service}
                    onClick={toggleDropdownServices}
                  >
                    <div className={styles.dropdown_button}>
                      {filterdata?.select_services_title}
                    </div>
                    <div className={styles.arrow}>
                      <Image
                        src={
                          openDropdown === 'services'
                            ? `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/drop_down_upside_0c7e8734bc.svg`
                            : `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/drop_down_0839569e62.svg`
                        }
                        alt={'dropdown arrow'}
                        width={24}
                        height={24}
                      />
                    </div>
                  </div>

                  {openDropdown === 'services' && (
                    <div className={styles.dropdown_content}>
                      <ul>
                        {dropDownServicesData?.map((service: any) => (
                          <li key={service.id}>
                            <label className={styles.checkbox_label}>
                              <input
                                type="checkbox"
                                checked={selectedServices.includes(
                                  service.attributes.service_title,
                                )}
                                onChange={() =>
                                  handleServiceChange(
                                    service.attributes.service_title,
                                  )
                                }
                              />
                              {service.attributes.service_title}
                            </label>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>

                {/* Industries Dropdown */}
                <div
                  ref={industryDropdownRef}
                  className={styles.dropdown_tabs_services}
                >
                  <div
                    className={styles.dropdown_service}
                    onClick={toggleDropdownIndustry}
                  >
                    <div className={styles.dropdown_button}>
                      {filterdata?.select_industries_title}
                    </div>
                    <div className={styles.arrow}>
                      <Image
                        src={
                          openDropdown === 'industry'
                            ? `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/drop_down_upside_0c7e8734bc.svg`
                            : `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/drop_down_0839569e62.svg`
                        }
                        alt={'dropdown arrow'}
                        width={24}
                        height={24}
                      />
                    </div>
                  </div>

                  {openDropdown === 'industry' && (
                    <div className={styles.dropdown_content}>
                      <ul>
                        {dropDownIndustryData?.map((industry: any) => (
                          <li key={industry.id}>
                            <label className={styles.checkbox_label}>
                              <input
                                type="checkbox"
                                checked={selectedIndustries.includes(
                                  industry.attributes.industry_title,
                                )}
                                onChange={() =>
                                  handleIndustryChange(
                                    industry.attributes.industry_title,
                                  )
                                }
                              />
                              {industry.attributes.industry_title}
                            </label>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>

              <Button
                className={styles.clear_button}
                label={'Clear'}
                type="submit"
                onClick={() => {
                  setSelectedServices([]);
                  setSelectedIndustries([]);
                }}
              />
            </div>
            <div className={styles.selected_value_container}>
              {/* Display combined selected values */}
              {combinedSelectedValues.map((value, index) => (
                <div className={styles.selected_value_box} key={index}>
                  {value}
                  <Image
                    src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/x_f7d037f941.svg`}
                    alt="Remove"
                    width={24}
                    height={24}
                    className={styles.close_icon}
                    onClick={() => removeSelectedValue(value)}
                  />
                </div>
              ))}
            </div>
          </Container>
          <Container fluid className={styles.container}>
            {/* Blogs cards */}
            <div className={styles.cardWrapper}>
              {filteredData.length > 0 ? (
                paginatedData.map((data, index) => {
                  return (
                    <Link
                      href={`/blog/${data?.attributes?.slug}`}
                      key={index}
                      className={styles.link}
                      prefetch={false}
                    >
                      <div className={styles.card_preview}>
                        <Image
                          loading="eager"
                          priority
                          key={
                            data?.attributes?.heroSection_image?.data
                              ?.attributes?.url
                          }
                          src={
                            data?.attributes?.heroSection_image?.data
                              ?.attributes?.format?.small?.url ||
                            data?.attributes?.heroSection_image?.data
                              ?.attributes?.formats?.small?.url ||
                            data?.attributes?.heroSection_image?.data
                              ?.attributes?.format?.medium?.url ||
                            data?.attributes?.heroSection_image?.data
                              ?.attributes?.formats?.medium?.url ||
                            data?.attributes?.heroSection_image?.data
                              ?.attributes?.url
                          }
                          alt={
                            data?.attributes?.heroSection_image?.data
                              ?.attributes?.alternativeText
                          }
                          className={styles.previewImage}
                          width={384}
                          height={220}
                        />
                        <Button
                          className={styles.date_button}
                          label={formatPublishDate(
                            data?.attributes?.publishedAt,
                          )}
                        />
                        <Heading
                          headingType="h3"
                          title={data?.attributes?.title}
                          className={styles.card_title}
                        />
                      </div>
                    </Link>
                  );
                })
              ) : (
                <div className={styles.no_data_found}>No data found.</div>
              )}
            </div>

            {/* Pagination Controls using ReactPaginate */}
            {filteredData.length > 0 && (
              <div className={styles.paginationContainer}>
                <ReactPaginate
                  previousLabel={'<'}
                  nextLabel={'>'}
                  pageCount={pageCount}
                  onPageChange={handlePageChange}
                  forcePage={currentPage}
                  containerClassName={styles.pagination}
                  previousLinkClassName={styles.paginationLink}
                  nextLinkClassName={styles.paginationLink}
                  disabledClassName={styles.paginationDisabled}
                  activeClassName={styles.paginationActive}
                />
              </div>
            )}
          </Container>
        </>
      ) : (
        <>
          <div className={styles.button_filter_mobile}>
            <Button
              className={styles.filter_button_mobile}
              label={'Filter'}
              type="submit"
              onClick={toggleFilterVisibility}
            />
          </div>

          {isFilterVisible && (
            <Container fluid className={styles.filter_container}>
              <Heading
                headingType="h2"
                title={filterdata?.title}
                className={styles.filter_title}
              />
              <div className={styles.filter_tabs}>
                <div className={styles.dropdown_tabs}>
                  {/* Services Dropdown */}
                  <div
                    ref={servicesDropdownRef}
                    className={styles.dropdown_tabs_services}
                  >
                    <div
                      className={styles.dropdown_service}
                      onClick={toggleDropdownServices}
                    >
                      <div className={styles.dropdown_button}>
                        {filterdata?.select_services_title}
                      </div>
                      <div className={styles.arrow}>
                        <Image
                          src={
                            openDropdown === 'services'
                              ? `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/drop_down_upside_0c7e8734bc.svg`
                              : `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/drop_down_0839569e62.svg`
                          }
                          alt={'dropdown arrow'}
                          width={24}
                          height={24}
                        />
                      </div>
                    </div>

                    {openDropdown === 'services' && (
                      <div className={styles.dropdown_content}>
                        <ul>
                          {dropDownServicesData?.map((service: any) => (
                            <li key={service.id}>
                              <label className={styles.checkbox_label}>
                                <input
                                  type="checkbox"
                                  checked={selectedServices.includes(
                                    service.attributes.service_title,
                                  )}
                                  onChange={() =>
                                    handleServiceChange(
                                      service.attributes.service_title,
                                    )
                                  }
                                />
                                {service.attributes.service_title}
                              </label>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  {/* Industries Dropdown */}
                  <div
                    ref={industryDropdownRef}
                    className={styles.dropdown_tabs_services}
                  >
                    <div
                      className={styles.dropdown_service}
                      onClick={toggleDropdownIndustry}
                    >
                      <div className={styles.dropdown_button}>
                        {filterdata?.select_industries_title}
                      </div>
                      <div className={styles.arrow}>
                        <Image
                          src={
                            openDropdown === 'industry'
                              ? `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/drop_down_upside_0c7e8734bc.svg`
                              : `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/drop_down_0839569e62.svg`
                          }
                          alt={'dropdown arrow'}
                          width={24}
                          height={24}
                        />
                      </div>
                    </div>

                    {openDropdown === 'industry' && (
                      <div className={styles.dropdown_content}>
                        <ul>
                          {dropDownIndustryData?.map((industry: any) => (
                            <li key={industry.id}>
                              <label className={styles.checkbox_label}>
                                <input
                                  type="checkbox"
                                  checked={selectedIndustries.includes(
                                    industry.attributes.industry_title,
                                  )}
                                  onChange={() =>
                                    handleIndustryChange(
                                      industry.attributes.industry_title,
                                    )
                                  }
                                />
                                {industry.attributes.industry_title}
                              </label>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className={styles.selected_value_container}>
                {/* Display combined selected values */}
                {combinedSelectedValues.map((value, index) => (
                  <div className={styles.selected_value_box} key={index}>
                    {value}
                    <Image
                      src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/x_f7d037f941.svg`}
                      alt="Remove"
                      width={24}
                      height={24}
                      className={styles.close_icon}
                      onClick={() => removeSelectedValue(value)}
                    />
                  </div>
                ))}
              </div>
            </Container>
          )}
          <Container fluid className={styles.container}>
            {/* Blog Listing cards */}
            <div className={styles.cardWrapper}>
              {filteredData.length > 0 ? (
                paginatedData.map((data, index) => (
                  <Link
                    href={`/blog/${data?.attributes?.slug}`}
                    key={index}
                    className={styles.link}
                    prefetch={false}
                  >
                    <div className={styles.card_preview}>
                      <Image
                        priority
                        loading="eager"
                        key={
                          data?.attributes?.heroSection_image?.data?.attributes
                            ?.url
                        }
                        src={
                          data?.attributes?.heroSection_image?.data?.attributes
                            ?.format?.small?.url ||
                          data?.attributes?.heroSection_image?.data?.attributes
                            ?.formats?.small?.url ||
                          data?.attributes?.heroSection_image?.data?.attributes
                            ?.format?.medium?.url ||
                          data?.attributes?.heroSection_image?.data?.attributes
                            ?.formats?.medium?.url ||
                          data?.attributes?.heroSection_image?.data?.attributes
                            ?.url
                        }
                        alt={
                          data?.attributes?.heroSection_image?.data?.attributes
                            ?.alternativeText
                        }
                        className={styles.previewImage}
                        width={348}
                        height={220}
                      />
                      <Button
                        className={styles.date_button}
                        label={formatPublishDate(data?.attributes?.publishedAt)}
                      />
                      <Heading
                        headingType="h3"
                        title={data?.attributes?.title}
                        className={styles.card_title}
                      />
                    </div>
                  </Link>
                ))
              ) : (
                <div className={styles.no_data_found}>No data found.</div>
              )}
            </div>

            {/* Pagination Controls using ReactPaginate */}
            {filteredData.length > 0 && (
              <div className={styles.paginationContainer}>
                <ReactPaginate
                  previousLabel={'<'}
                  nextLabel={'>'}
                  pageCount={pageCount}
                  onPageChange={handlePageChange}
                  forcePage={currentPage}
                  containerClassName={styles.pagination}
                  previousLinkClassName={styles.paginationLink}
                  nextLinkClassName={styles.paginationLink}
                  disabledClassName={styles.paginationDisabled}
                  activeClassName={styles.paginationActive}
                />
              </div>
            )}
          </Container>
        </>
      )}
    </>
  );
}
