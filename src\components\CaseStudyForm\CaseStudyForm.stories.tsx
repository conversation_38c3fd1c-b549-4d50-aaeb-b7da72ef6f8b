import CaseStudyForm from './CaseStudyForm';

export default {
  title: 'Components/CaseStudyForm',
};

const data = {
  data: {
    id: 1,
    attributes: {
      createdAt: '2024-06-05T12:35:35.535Z',
      updatedAt: '2024-06-21T12:05:56.623Z',
      publishedAt: '2024-06-05T12:35:38.268Z',
      Industries: {
        id: 1,
        title: 'Industries',
        industriesCardsBox: [
          {
            id: 1,
            title: 'E-Commerce',
            description:
              "<p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.</p>",
            button: {
              id: 1,
              title: null,
              link: '#',
            },
          },
          {
            id: 2,
            title: 'E-Commerce2',
            description:
              "<p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.</p>",
            button: {
              id: 2,
              title: null,
              link: '#',
            },
          },
          {
            id: 3,
            title: 'E-Commerce3',
            description:
              "<p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.</p>",
            button: {
              id: 3,
              title: null,
              link: '#',
            },
          },
        ],
        backgroundImage: {
          data: {
            id: 38,
            attributes: {
              name: 'bg-image.jpeg',
              alternativeText: null,
              caption: null,
              width: 1920,
              height: 617,
              formats: {
                thumbnail: {
                  name: 'thumbnail_bg-image.jpeg',
                  hash: 'thumbnail_bg_image_c059f3b63d',
                  ext: '.jpeg',
                  mime: 'image/jpeg',
                  path: null,
                  width: 245,
                  height: 79,
                  size: 6.72,
                  sizeInBytes: 6720,
                  url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/thumbnail_bg_image_c059f3b63d.jpeg`,
                },
                small: {
                  name: 'small_bg-image.jpeg',
                  hash: 'small_bg_image_c059f3b63d',
                  ext: '.jpeg',
                  mime: 'image/jpeg',
                  path: null,
                  width: 500,
                  height: 160,
                  size: 20.98,
                  sizeInBytes: 20983,
                  url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/small_bg_image_c059f3b63d.jpeg`,
                },
                large: {
                  name: 'large_bg-image.jpeg',
                  hash: 'large_bg_image_c059f3b63d',
                  ext: '.jpeg',
                  mime: 'image/jpeg',
                  path: null,
                  width: 1000,
                  height: 321,
                  size: 62.42,
                  sizeInBytes: 62421,
                  url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/large_bg_image_c059f3b63d.jpeg`,
                },
                medium: {
                  name: 'medium_bg-image.jpeg',
                  hash: 'medium_bg_image_c059f3b63d',
                  ext: '.jpeg',
                  mime: 'image/jpeg',
                  path: null,
                  width: 750,
                  height: 241,
                  size: 40.57,
                  sizeInBytes: 40572,
                  url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/medium_bg_image_c059f3b63d.jpeg`,
                },
              },
              hash: 'bg_image_c059f3b63d',
              ext: '.jpeg',
              mime: 'image/jpeg',
              size: 163.68,
              url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/bg_image_c059f3b63d.jpeg`,
              previewUrl: null,
              provider:
                '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2024-06-18T09:47:13.265Z',
              updatedAt: '2024-06-18T09:47:13.265Z',
            },
          },
        },
      },
    },
  },
  meta: {},
};

export function CaseStudyFormStory() {
  return (
    <div>
      <CaseStudyForm formData={data} />
    </div>
  );
}
