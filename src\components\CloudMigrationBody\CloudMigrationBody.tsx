'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import styles from './CloudMigrationBody.module.css';
import useMediaQueryState from '@hooks/useMediaQueryState';

import CloudMigrationQuestionAndAnswers from '@components/CloudMigrationQuestionAndAnswers';
import HeroSection from '@components/HeroSection';
import CloudMigrationStep from '@components/CloudMigrationStep';
import CloudMigrationForm from '@components/CloudMigrationForm';
import CloudMigrationResults from '@components/CloudMigrationResults';

export default function CloudMigrationBody({ body, formData }) {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [visibleSection, setVisibleSection] = useState(null);
  const [result, setResult] = useState(null);
  const [URL, setURL] = useState([]);
  const router = useRouter();

  const isTablet = useMediaQueryState({
    query: `(max-width: 700px)`,
  });

  useEffect(() => {
    let newURL = [...URL];
    for (let i = 0; i < body?.cloud_migration_components?.data.length; i++) {
      newURL.push(
        body?.cloud_migration_components?.data[i].attributes.heading
          .toLowerCase()
          .replaceAll(' ', '-')
          .replaceAll('&', 'and'),
      );
    }
    newURL.push('result');
    setURL(newURL);

    initializeStates();

    function handleHashChange() {
      let currentHash = location.hash.substring(1);

      if (
        (currentHash === newURL[newURL.length - 2] &&
          localStorage.getItem('cloudMigrationResult') !== null) ||
        (currentHash === newURL[newURL.length - 1] &&
          localStorage.getItem('cloudMigrationResult') === null)
      ) {
        handleRestart();
      } else {
        handleVisibleSection(newURL.indexOf(currentHash));
      }
    }

    addEventListener('hashchange', handleHashChange);
    return () => removeEventListener('hashchange', handleHashChange);
  }, []);

  useEffect(() => {
    let url = '';
    if (visibleSection < body?.cloud_migration_components?.data.length) {
      url = URL[visibleSection];
    } else {
      url = 'result';
    }
    router.push('#' + url, { scroll: false });

    const element = document.getElementById(body?.hero_section?.button_link);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }, [visibleSection]);

  function initializeStates() {
    let newData = [];
    let newError = [];
    let newVisibleSection = 0;
    let newResult = null;

    if (localStorage.getItem('cloudMigrationResult') !== null) {
      newResult = JSON.parse(localStorage.getItem('cloudMigrationResult'));
    }

    if (
      localStorage.getItem('cloudMigrationData') !== null &&
      localStorage.getItem('cloudMigrationError') !== null
    ) {
      newData = JSON.parse(localStorage.getItem('cloudMigrationData'));
      newError = JSON.parse(localStorage.getItem('cloudMigrationError'));
    } else {
      for (let i = 0; i < body?.cloud_migration_components?.data.length; i++) {
        let arrData = [];
        let arrError = [];

        const questions =
          body?.cloud_migration_components?.data[i]?.attributes?.questions ||
          [];

        for (let j = 0; j < questions.length; j++) {
          if (questions[j].type === 'mcq') {
            arrData.push([null, null]);
            arrError.push(null);
          } else if (questions[j].type === 'draggable') {
            // For draggable questions, initialize with first answer and corresponding slider value
            const firstAnswer = questions[j].answers[0].name;
            const sliderValue = 0; // First answer corresponds to slider position 0
            arrData.push([firstAnswer, sliderValue]);
            arrError.push(false);
          } else {
            arrData.push([questions[j].answers[0].name, 0]);
            arrError.push(false);
          }
        }
        newData[i] = arrData;
        newError[i] = arrError;
      }
    }

    if (localStorage.getItem('cloudMigrationVisibleSection') !== null) {
      newVisibleSection = JSON.parse(
        localStorage.getItem('cloudMigrationVisibleSection'),
      );
    }

    setData(newData);
    setError(newError);
    setVisibleSection(newVisibleSection);
    setResult(newResult);
  }

  function handleData(sectionIndex, questionIndex, name, value) {
    const newData = [...data];
    newData[sectionIndex][questionIndex][0] = name;
    newData[sectionIndex][questionIndex][1] = value;
    localStorage.setItem('cloudMigrationData', JSON.stringify(newData));
    setData(newData);
  }

  function handleError(sectionIndex, questionIndex, value) {
    const newError = [...error];
    newError[sectionIndex][questionIndex] = value;
    localStorage.setItem('cloudMigrationError', JSON.stringify(newError));
    setError(newError);
  }

  function handleVisibleSection(value) {
    if (value === -1) {
      value = 0;
    }
    localStorage.setItem('cloudMigrationVisibleSection', JSON.stringify(value));
    setVisibleSection(value);
  }

  function handlePrevious() {
    handleVisibleSection(visibleSection - 1);
  }

  function canGoToNext() {
    if (
      error[visibleSection].includes(null) ||
      error[visibleSection].includes(true)
    ) {
      let newError = [...error];
      for (let i = 0; i < newError[visibleSection].length; i++) {
        if (newError[visibleSection][i] === null) {
          newError[visibleSection][i] = true;
        }
      }
      localStorage.setItem('cloudMigrationError', JSON.stringify(newError));
      setError(newError);
      return false;
    } else {
      return true;
    }
  }

  // Cost calculation logic based on the spreadsheet
  function calculateMigrationCost() {
    if (!canGoToNext()) return null;

    let totalCost = 0;
    const costFactors = {
      serverCount: 0,
      dataCapacity: 0,
      highAvailability: 0,
      environments: 0,
      compliance: 0,
      migrationStrategy: 0,
      autoScaling: 0,
    };

    // Process each section's answers based on spreadsheet logic
    for (let i = 0; i < data.length; i++) {
      const questions =
        body?.cloud_migration_components?.data[i]?.attributes?.question ||
        body?.cloud_migration_components?.data[i]?.attributes?.questions ||
        [];

      for (let j = 0; j < data[i].length; j++) {
        const questionData = questions[j];
        const answerValue = data[i][j][1];
        const answerName = data[i][j][0];

        // Apply specific cost logic based on spreadsheet
        if (questionData?.name) {
          const questionName = questionData.name.toLowerCase();

          // Server count costs (Question 2)
          if (questionName.includes('how many servers')) {
            // Use the value from the answer object if available
            const answer = questionData.answers?.find(
              ans => ans.name === answerName,
            );
            if (answer && answer.value) {
              costFactors.serverCount = answer.value;
            } else {
              // Fallback to text matching
              if (answerName?.includes('Less than 10'))
                costFactors.serverCount = 10000;
              else if (answerName?.includes('10 – 50'))
                costFactors.serverCount = 50000;
              else if (answerName?.includes('50 – 100'))
                costFactors.serverCount = 250000;
              else if (answerName?.includes('More than 100'))
                costFactors.serverCount = 500000;
            }
          }

          // Data capacity costs (Question 5)
          else if (questionName.includes('total capacity')) {
            if (answerName?.includes('10 GB – 50 GB'))
              costFactors.dataCapacity = 100;
            else if (answerName?.includes('50 GB – 200 GB'))
              costFactors.dataCapacity = 500;
            else if (answerName?.includes('200 GB – 1 TB'))
              costFactors.dataCapacity = 2000;
            else if (answerName?.includes('1 TB – 10 TB'))
              costFactors.dataCapacity = 10000;
            else if (answerName?.includes('10 TB – 50 TB'))
              costFactors.dataCapacity = 50000;
            else if (answerName?.includes('> 50 TB'))
              costFactors.dataCapacity = 150000;
          }

          // High availability (Question 10)
          else if (
            questionName.includes('high availability') ||
            questionName.includes('disaster recovery')
          ) {
            if (answerName?.toLowerCase().includes('yes')) {
              costFactors.highAvailability = 1; // Flag to add 20% later
            }
          }

          // Environment costs (Question 13)
          else if (questionName.includes('cloud environments')) {
            if (answerName?.includes('Dev')) costFactors.environments += 10000;
            if (answerName?.includes('Test')) costFactors.environments += 15000;
            if (answerName?.includes('Staging'))
              costFactors.environments += 20000;
            if (answerName?.includes('Production'))
              costFactors.environments += 100000;
          }

          // Compliance costs (Question 14)
          else if (
            questionName.includes('compliance') ||
            questionName.includes('regulatory')
          ) {
            if (answerName?.includes('HIPAA')) costFactors.compliance += 20000;
            if (answerName?.includes('GDPR')) costFactors.compliance += 10000;
            if (answerName?.includes('PCI DSS'))
              costFactors.compliance += 15000;
            if (answerName?.includes('SOC 2')) costFactors.compliance += 15000;
            if (answerName?.includes('CCPA')) costFactors.compliance += 5000;
            if (answerName?.includes('FedRAMP'))
              costFactors.compliance += 50000;
          }

          // Migration strategy costs (Question 15)
          else if (questionName.includes('migration strategy')) {
            if (answerName?.includes('Lift-and-shift'))
              costFactors.migrationStrategy = 5000;
            else if (answerName?.includes('Re-platforming'))
              costFactors.migrationStrategy = 50000;
            else if (answerName?.includes('Re-architecting'))
              costFactors.migrationStrategy = 150000;
            else if (answerName?.includes('Hybrid'))
              costFactors.migrationStrategy = 100000;
          }

          // Auto-scaling costs (Question 16)
          else if (questionName.includes('auto-scaling')) {
            if (answerName?.toLowerCase().includes('yes')) {
              costFactors.autoScaling = 10000;
            }
          }
        }
      }
    }

    // Calculate total cost based on spreadsheet logic
    totalCost = costFactors.serverCount + costFactors.dataCapacity;

    // Add high availability cost (20% of infrastructure + data)
    if (costFactors.highAvailability > 0) {
      const haAdditionalCost =
        (costFactors.serverCount + costFactors.dataCapacity) * 0.2;
      totalCost += haAdditionalCost;
      costFactors.highAvailability = haAdditionalCost;
    }

    // Add other costs
    totalCost += costFactors.environments;
    totalCost += costFactors.compliance;
    totalCost += costFactors.migrationStrategy;
    totalCost += costFactors.autoScaling;

    const lowerRange = Math.round(totalCost);
    const upperRange = Math.round(totalCost * 1.3); // 30% higher as per spreadsheet

    const newResult = {
      lowerRange,
      upperRange,
      totalCost: lowerRange,
      costFactors,
    };

    localStorage.setItem('cloudMigrationResult', JSON.stringify(newResult));
    setResult(newResult);
    return { data, newResult };
  }

  function handleRestart() {
    localStorage.removeItem('cloudMigrationData');
    localStorage.removeItem('cloudMigrationError');
    localStorage.removeItem('cloudMigrationVisibleSection');
    localStorage.removeItem('cloudMigrationResult');
    localStorage.removeItem('subAnswer');
    router.push(
      '/cloud-migration-cost-calculator#' +
        body?.cloud_migration_components?.data[0].attributes.heading
          .toLowerCase()
          .replaceAll(' ', '-')
          .replaceAll('&', 'and'),
    );
    initializeStates();
  }

  return (
    <>
      {data && visibleSection < data.length && (
        <HeroSection heroData={body?.hero_section} variant="cloud-migration" />
      )}
      {data && (
        <div className={styles.container} id={body?.hero_section?.button_link}>
          {visibleSection < data.length && (
            <>
              {isTablet ? (
                <>
                  <div className={styles.button_wrapper_mobile}>
                    {visibleSection > 0 && visibleSection < data.length && (
                      <button onClick={handlePrevious}>
                        <Image
                          src="https://cdn.marutitech.com/black_chevron_left_5adc2eb9de.svg"
                          alt="previous section"
                          width={25}
                          height={25}
                        />
                      </button>
                    )}
                    {visibleSection + 1}/0{URL.length - 1}
                    {visibleSection < data.length - 1 && (
                      <button
                        onClick={() => {
                          if (canGoToNext())
                            handleVisibleSection(visibleSection + 1);
                        }}
                      >
                        <Image
                          src="https://cdn.marutitech.com/black_chevron_left_6d81dc24e5.svg"
                          alt="next section"
                          width={25}
                          height={25}
                        />
                      </button>
                    )}
                  </div>
                </>
              ) : (
                <CloudMigrationStep
                  visibleCount={visibleSection}
                  sections={body?.cloud_migration_components?.data}
                  onStepClick={stepNumber =>
                    handleVisibleSection(stepNumber - 1)
                  }
                />
              )}
            </>
          )}

          {body?.cloud_migration_components?.data.map(
            (section, sectionIndex) => (
              <div
                key={sectionIndex}
                className={
                  visibleSection === sectionIndex
                    ? styles.section_wrapper
                    : styles.hidden
                }
              >
                <div className={styles.heading}>
                  <h2>
                    {sectionIndex + 1}. {section?.attributes?.heading}
                  </h2>
                </div>
                {visibleSection !== data.length && (
                  <CloudMigrationQuestionAndAnswers
                    sectionIndex={sectionIndex}
                    sectionQuestions={section?.attributes.questions || []}
                    sectionData={data[sectionIndex]}
                    sectionError={error[sectionIndex]}
                    handleData={handleData}
                    handleError={handleError}
                  />
                )}
                <span id="error">
                  {visibleSection < data.length &&
                    error[visibleSection].includes(true) && (
                      <div className={styles.error_message}>
                        Please fill all the required fields.
                      </div>
                    )}
                </span>

                {visibleSection === data.length - 1 && (
                  <CloudMigrationForm
                    formData={formData}
                    handleResult={calculateMigrationCost}
                    handleVisibleSection={handleVisibleSection}
                  />
                )}
                <div className={styles.button_wrapper}>
                  {visibleSection > 0 && visibleSection < data.length && (
                    <button onClick={handlePrevious}>
                      <Image
                        src="https://cdn.marutitech.com/chevron_left_7f3e8fa9d6.svg"
                        alt="previous section"
                        width={50}
                        height={50}
                      />
                    </button>
                  )}
                  {visibleSection < data.length - 1 && (
                    <button
                      onClick={() => {
                        if (canGoToNext())
                          handleVisibleSection(visibleSection + 1);
                      }}
                    >
                      <Image
                        src="https://cdn.marutitech.com/chevron_right_0f9e1dff3c.svg"
                        alt="next section"
                        width={50}
                        height={50}
                      />
                    </button>
                  )}
                </div>
              </div>
            ),
          )}

          {result && visibleSection === data.length && (
            <CloudMigrationResults
              result={result}
              body={body}
              handleRestart={handleRestart}
            />
          )}
        </div>
      )}
    </>
  );
}
