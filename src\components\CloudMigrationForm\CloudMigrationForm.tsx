'use client';

import React from 'react';
import AIReadinessForm from '@components/AIReadinessForm';

interface CloudMigrationFormProps {
  formData: any;
  handleResult: () => any;
  handleVisibleSection: (section: number) => void;
}

export default function CloudMigrationForm({
  formData,
  handleResult,
  handleVisibleSection,
}: CloudMigrationFormProps) {
  return (
    <AIReadinessForm
      formData={formData}
      source="Cloud Migration Cost Calculator"
      handleResult={handleResult}
      handleVisibleSection={handleVisibleSection}
      submitButtonLabel="Check my Cloud Migration Cost"
      formType="cloudMigration"
    />
  );
}
