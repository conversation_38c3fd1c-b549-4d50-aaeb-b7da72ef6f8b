'use client';

import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Button from '@components/Button';
import Heading from '@components/Heading';
import styles from './CloudMigrationResults.module.css';

export default function CloudMigrationResults({ result, body, handleRestart }) {
  const router = useRouter();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className={styles.results_wrapper}>
      {/* Restart Button */}
      <div className={styles.restart_button_wrapper}>
        <Button
          className={styles.restart_button}
          onClick={handleRestart}
          type="button"
        >
          <Image
            src="https://cdn.marutitech.com/restart_button_831deeb022.svg"
            alt="restart"
            width={20}
            height={20}
          />
          Restart
        </Button>
      </div>

      {/* Main Heading */}
      <div className={styles.results_header}>
        <Heading
          title="Your Estimated Cloud Migration Cost"
          headingType="h1"
          className={styles.main_heading}
        />
      </div>

      {/* Cost Display */}
      <div className={styles.cost_display}>
        <div className={styles.cost_range}>
          {formatCurrency(result.lowerRange)} –{' '}
          {formatCurrency(result.upperRange)} USD
        </div>
      </div>

      {/* Disclaimer */}
      <div className={styles.disclaimer}>
        <p>
          <strong>Disclaimer:</strong> This estimate is a ballpark figure
          derived from typical migration patterns and industry benchmarks.
          Actual costs can vary depending on your unique setup, compliance
          needs, application complexity, and performance expectations. A more
          accurate quote will require a detailed discussion with our experts.
        </p>
      </div>

      {/* Turn Your Estimate Into Real Savings Section */}
      <div className={styles.savings_section}>
        <Heading
          title="Turn Your Estimate Into Real Savings"
          headingType="h2"
          className={styles.savings_heading}
        />
        <p className={styles.savings_description}>
          Our experienced cloud consultants can help you optimize your strategy,
          avoid hidden costs, and potentially reduce your migration expenses
          through tailored planning, architecture optimization, and vendor
          selection.
        </p>

        <div className={styles.benefits_grid}>
          <div className={styles.benefit_card}>
            <h3 className={styles.benefit_title}>
              Slash Your Cloud Spend by up to 30%
            </h3>
            <p className={styles.benefit_description}>
              through customized cost-optimization strategies that align
              resources to demand and eliminate wasteful spend.
            </p>
          </div>

          <div className={styles.benefit_card}>
            <h3 className={styles.benefit_title}>Accelerate Time-to-Value</h3>
            <p className={styles.benefit_description}>
              with clear, phased migration roadmaps that minimize downtime and
              keep your business running smoothly throughout every stage.
            </p>
          </div>

          <div className={styles.benefit_card}>
            <h3 className={styles.benefit_title}>
              Maximize ROI with Expert Guidance
            </h3>
            <p className={styles.benefit_description}>
              avoid hidden costs and costly missteps by leveraging our deep
              migration expertise and proven methodologies.
            </p>
          </div>
        </div>

        <div className={styles.cta_button_wrapper}>
          <Button
            className={styles.cta_button}
            label="Save Up to 30% Now"
            type="button"
            onClick={() => {
              router.push(body?.consultation_button?.link || '/contact-us');
            }}
          />
        </div>
      </div>
    </div>
  );
}
