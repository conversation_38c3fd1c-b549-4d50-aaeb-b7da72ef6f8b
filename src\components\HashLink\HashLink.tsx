'use client';

import React, { ReactNode, CSSProperties, ReactEventHandler } from 'react';
import NextLink from 'next/link';
import classNames from '@utils/classNames';
import { handleHashNavigation } from '@utils/hashNavigation';

interface HashLinkProps {
  id?: string;
  children?: ReactNode;
  href?: string;
  className?: string;
  style?: CSSProperties;
  isExternal?: boolean;
  onClick?: ReactEventHandler;
  scroll?: boolean;
  shallow?: boolean;
  ariaLabel?: string;
  dataID?: string;
  suppressHydrationWarning?: boolean;
  /**
   * Whether to use hash navigation handling for internal links with hash fragments
   */
  useHashNavigation?: boolean;
}

/**
 * Enhanced Link component that properly handles hash navigation
 * for static sites with consistent behavior between local and production
 */
export default function HashLink({
  id,
  children,
  href = '',
  className,
  style,
  isExternal = false,
  onClick,
  scroll = true,
  shallow = false,
  ariaLabel,
  dataID = null,
  suppressHydrationWarning = false,
  useHashNavigation = true,
}: HashLinkProps) {
  
  const handleClick = (event: React.MouseEvent) => {
    // Call the original onClick handler if provided
    if (onClick) {
      onClick(event);
    }

    // Handle hash navigation for internal links
    if (!isExternal && useHashNavigation && href.includes('#')) {
      event.preventDefault();
      handleHashNavigation(href, () => {
        // Additional click handling can go here
      });
      return;
    }
  };

  // External links
  if (isExternal) {
    return (
      <a
        suppressHydrationWarning={suppressHydrationWarning}
        id={id}
        className={classNames(className)}
        href={href}
        style={style}
        target="_blank"
        rel="noreferrer"
        aria-label={ariaLabel}
        onClick={handleClick}
        data-id={dataID}
      >
        {children}
      </a>
    );
  }

  // Internal links with hash navigation
  if (useHashNavigation && href.includes('#')) {
    return (
      <a
        suppressHydrationWarning={suppressHydrationWarning}
        id={id}
        className={classNames(className)}
        href={href}
        style={style}
        onClick={handleClick}
        data-id={dataID}
        aria-label={ariaLabel}
      >
        {children}
      </a>
    );
  }

  // Regular internal links (use Next.js Link)
  return (
    <NextLink
      href={href}
      scroll={scroll}
      shallow={shallow}
      suppressHydrationWarning={suppressHydrationWarning}
      id={id}
      className={classNames(className)}
      style={style}
      onClick={handleClick}
      data-id={dataID}
      aria-label={ariaLabel}
    >
      {children}
    </NextLink>
  );
}
