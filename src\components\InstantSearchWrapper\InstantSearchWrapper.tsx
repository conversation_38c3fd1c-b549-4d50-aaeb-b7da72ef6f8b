'use client';

import React from 'react';
import { algoliasearch } from 'algoliasearch';
import { Container } from 'react-bootstrap';
import {
  InstantSearch,
  SearchBox,
  Hits,
  Configure,
  useInstantSearch,
  useSearchBox,
  Index,
  SortBy,
  useHits,
  useStats,
} from 'react-instantsearch';
import style from './InstantSearchWrapper.module.css';
import Image from 'next/image';
import Link from 'next/link';

import Heading from '@components/Heading';

const searchClient = algoliasearch(
  process.env.NEXT_PUBLIC__ALGOLIA_APP_ID,
  process.env.NEXT_PUBLIC__ALGOLIA_PUBLIC_API_KEY,
);

let blogInitialResults = [];
let caseStudiesInitialResults = [];
let serviceInitialResults = [];
let industryInitialResults = [];
let partnerInitialResults = [];
let otherInitialResults = [];

function RenderSearchSection({ searchPageInitialData }) {
  return (
    <div className={style.searchSection}>
      <div className={style.inputLabel}>
        {searchPageInitialData?.search_input_title}
      </div>
      <SearchBox
        placeholder="Enter the search term"
        classNames={{
          root: `${style.inputContainer}`,
          input: `${style.searchInput}`,
        }}
        submitIconComponent={() => (
          <Image
            src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/search_765eaf6a84.svg`}
            className={style.searchButton}
            width={24}
            height={24}
            alt="Search Icon"
          />
        )}
        resetIconComponent={() => (
          <Image
            src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/Cancel_2452275c91.svg`}
            className={style.resetButton}
            width={20}
            height={20}
            alt="Reset Icon"
          />
        )}
      />
    </div>
  );
}

function RenderResultsSection({ searchPageInitialData }) {
  const { query } = useSearchBox(); // Get the current query
  const { nbHits } = useStats();
  // const { status } = useInstantSearch();

  // if (status === 'loading' || status === 'stalled') {
  //   return <p>Loading search results</p>;
  // }

  return (
    <div className={style.searchResultsSection}>
      <div className={style.searchResultsHeader}>
        <div className={style.allResultsTitle}>
          {searchPageInitialData?.all_results_title}
        </div>
        {query.trim() && nbHits > 30 && (
          <div
            dangerouslySetInnerHTML={{
              __html: searchPageInitialData?.showing_top_20_results_title,
            }}
          />
        )}
      </div>
      <div className={style.customHits}>
        {query.trim() ? (
          SearchResultHits()
        ) : (
          <InitialHits
            searchPageInitialData={
              searchPageInitialData?.initial_search_results
            }
          />
        )}
      </div>

      {query.trim() && nbHits > 30 && (
        <div
          className={style.searchResultsFooter}
          dangerouslySetInnerHTML={{
            __html: searchPageInitialData?.showing_top_20_results_title,
          }}
        />
      )}
    </div>
  );
}

function InitialHits(searchPageInitialData) {
  if (
    searchPageInitialData &&
    searchPageInitialData?.searchPageInitialData !== undefined
  ) {
    blogInitialResults =
      searchPageInitialData?.searchPageInitialData?.blogs?.data;
    caseStudiesInitialResults =
      searchPageInitialData?.searchPageInitialData?.case_studies?.data;
    serviceInitialResults = [
      ...searchPageInitialData?.searchPageInitialData?.l2_service_pages?.data,
      ...searchPageInitialData?.searchPageInitialData?.l3_service_pages?.data,
    ];
    industryInitialResults =
      searchPageInitialData?.searchPageInitialData?.industries?.data;
    partnerInitialResults =
      searchPageInitialData?.searchPageInitialData?.partners?.data;
    otherInitialResults =
      searchPageInitialData?.searchPageInitialData
        ?.custom_data_for_initial_search_results;
  }

  let title = 'Untitled';
  let url = `${process.env.NEXT_PUBLIC_SITE_URL}`;

  return (
    <>
      {searchPageInitialData &&
      searchPageInitialData?.searchPageInitialData !== undefined ? (
        <>
          {blogInitialResults.length > 0 && (
            <>
              <div className={style.resultsCategoryTitle}>Blogs</div>
              <div>
                {blogInitialResults.slice(0, 5).map((item, index) => {
                  title = item?.attributes?.title;
                  url = `${process.env.NEXT_PUBLIC_SITE_URL}/blog/${item?.attributes?.slug}`;
                  let blogPublisedDate = item?.attributes?.publishedAt;

                  return renderResult(title, url, index, blogPublisedDate);
                })}
              </div>
            </>
          )}
          {caseStudiesInitialResults.length > 0 && (
            <>
              <div className={style.resultsCategoryTitle}>Case Studies</div>
              <div>
                {caseStudiesInitialResults.slice(0, 5).map((item, index) => {
                  title = item?.attributes?.title;
                  url = `${process.env.NEXT_PUBLIC_SITE_URL}/case-study/${item?.attributes?.slug}`;

                  return renderResult(title, url, index);
                })}
              </div>
            </>
          )}
          {serviceInitialResults.length > 0 && (
            <>
              <div className={style.resultsCategoryTitle}>Services</div>
              <div>
                {serviceInitialResults.slice(0, 5).map((item, index) => {
                  title = item?.attributes?.pageName;
                  url = `${process.env.NEXT_PUBLIC_SITE_URL}/services/${item?.attributes?.slug}`;

                  return renderResult(title, url, index);
                })}
              </div>
            </>
          )}
          {industryInitialResults.length > 0 && (
            <>
              <div className={style.resultsCategoryTitle}>Industries</div>
              <div>
                {industryInitialResults.slice(0, 5).map((item, index) => {
                  title = item?.attributes?.pageName;
                  url = `${process.env.NEXT_PUBLIC_SITE_URL}/${item?.attributes?.slug}`;

                  return renderResult(title, url, index);
                })}
              </div>
            </>
          )}
          {partnerInitialResults.length > 0 && (
            <>
              <div className={style.resultsCategoryTitle}>Partners</div>
              <div>
                {partnerInitialResults.slice(0, 5).map((item, index) => {
                  title = item?.attributes?.page_name;
                  url = `${process.env.NEXT_PUBLIC_SITE_URL}/partners/${item?.attributes?.slug}`;

                  return renderResult(title, url, index);
                })}
              </div>
            </>
          )}
          {otherInitialResults.length > 0 && (
            <>
              <div className={style.resultsCategoryTitle}>Others</div>
              <div>
                {otherInitialResults.slice(0, 5).map((item, index) => {
                  title = item?.page_title;
                  url = item?.page_full_url;

                  return renderResult(title, url, index);
                })}
              </div>
            </>
          )}
        </>
      ) : (
        <div className={style.noResults}>Please add query in search box.</div>
      )}
    </>
  );
}

function SearchResultHits() {
  return (
    <NoResultsBoundary fallback={<NoResults />}>
      <Index indexName={process.env.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS}>
        <div className={style.resultsCategoryHeader}>
          <div className={style.resultsCategoryTitleWithSorting}>Blogs</div>
          <div className={style.resultsCategorySorting}>
            <span>Sort By:</span>
            <SortBy
              className={style.sortByDropdownDiv}
              defaultValue={process.env.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS}
              items={[
                {
                  value: process.env.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS,
                  label: 'Default',
                },
                {
                  value: process.env.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_DESC,
                  label: 'Latest',
                },
                {
                  value: process.env.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_ASC,
                  label: 'Oldest',
                },
              ]}
            />
          </div>
        </div>
        <NoResultsBoundary fallback={<NoResults pageType="Blogs" />}>
          <Hits hitComponent={Hit} />
        </NoResultsBoundary>
      </Index>

      <Index
        indexName={process.env.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_CASE_STUDIES}
      >
        <div className={style.resultsCategoryTitle}>Case Studies</div>
        <NoResultsBoundary fallback={<NoResults pageType="Case Studies" />}>
          <Hits hitComponent={Hit} />
        </NoResultsBoundary>
      </Index>

      <Index
        indexName={process.env.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_SERVICE_PAGES}
      >
        <div className={style.resultsCategoryTitle}>Services</div>
        <NoResultsBoundary fallback={<NoResults pageType="Services" />}>
          <Hits hitComponent={Hit} />
        </NoResultsBoundary>
      </Index>

      <Index
        indexName={process.env.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_INDUSTRY_PAGES}
      >
        <div className={style.resultsCategoryTitle}>Industries</div>
        <NoResultsBoundary fallback={<NoResults pageType="Industries" />}>
          <Hits hitComponent={Hit} />
        </NoResultsBoundary>
      </Index>

      <Index
        indexName={process.env.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_PARTNER_PAGES}
      >
        <div className={style.resultsCategoryTitle}>Partners</div>
        <NoResultsBoundary fallback={<NoResults pageType="Partners" />}>
          <Hits hitComponent={Hit} />
        </NoResultsBoundary>
      </Index>

      <Index indexName={process.env.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OTHER_PAGES}>
        <div className={style.resultsCategoryTitle}>Others</div>
        <NoResultsBoundary fallback={<NoResults pageType="Others" />}>
          <HitOthers />
        </NoResultsBoundary>
      </Index>
    </NoResultsBoundary>
  );
}

function NoResultsBoundary({ children, fallback }) {
  const { results } = useInstantSearch();

  // The `__isArtificial` flag makes sure not to display the No Results message
  // when no hits have been returned.
  if (!results.__isArtificial && results.nbHits === 0) {
    return (
      <>
        {fallback}
        <div hidden>{children}</div>
      </>
    );
  }

  return children;
}

function NoResults(pageType?) {
  return (
    <p className={style.noResults}>
      {pageType?.pageType
        ? `No results found in the \'${pageType?.pageType}\' for the provided search query.`
        : 'No results found for the provided search query.'}
    </p>
  );
}

function Hit({ hit }) {
  return renderResult(
    hit?.hierarchy?.lvl0,
    hit.url,
    hit?.objectID,
    hit?.blogPublishedDate,
  );
}

function getUniqueHitsByURL(hits) {
  const URLs = [];
  return hits.filter(obj => {
    if (URLs.includes(obj.url)) {
      return false;
    } else {
      URLs.push(obj.url);
      return true;
    }
  });
}

function HitOthers() {
  const { items } = useHits();
  const uniqueHits = getUniqueHitsByURL(items);

  return uniqueHits.map(hit => Hit({ hit }));
}

function renderResult(title, url, index?, blogPublishedDate?) {
  url = url.split('#')[0];
  blogPublishedDate = blogPublishedDate
    ? new Date(blogPublishedDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      })
    : null;

  return (
    <Link href={url} target="_blank" className={style.hitContent} key={index}>
      <div className={style.hitContentWrapper}>
        <Heading
          headingType="h5"
          richTextValue={title}
          className={style.searchHitTitle}
        />
        {blogPublishedDate && (
          <div className={style.searchHitDate}>{blogPublishedDate}</div>
        )}
      </div>
      <Image
        className={style.hitArrow}
        width={40}
        height={75}
        src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/search_result_hover_137c96ff4a.svg`}
        alt="Link arrow"
      />
    </Link>
  );
}

function InstantSearchWrapper({ searchPageInitialData }) {
  return (
    <Container fluid className={style.searchContainer}>
      <InstantSearch
        searchClient={searchClient}
        indexName={process.env.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OVERALL_SITE}
        initialUiState={{
          [process.env.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OVERALL_SITE]: {
            query: '',
          },
        }}
        routing={true}
      >
        <Configure hitsPerPage={5} />
        <RenderSearchSection searchPageInitialData={searchPageInitialData} />
        <RenderResultsSection searchPageInitialData={searchPageInitialData} />
      </InstantSearch>
    </Container>
  );
}

export default InstantSearchWrapper;
