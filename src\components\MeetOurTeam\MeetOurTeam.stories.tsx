import MeetOurTeam from './MeetOurTeam';

export default {
  title: 'Components/Meet Our Team',
};

const data = {
  data: {
    id: 1,
    attributes: {
      createdAt: '2024-07-31T05:20:54.419Z',
      updatedAt: '2024-08-22T13:34:52.762Z',
      publishedAt: '2024-07-31T05:20:56.020Z',
      meet_our_team: {
        id: 1,
        main_title: 'Meet Our Team',
        card_box: [
          {
            id: 1,
            card_description: '<p>Head of Business Development</p>',
            card_title: '<PERSON><PERSON><PERSON> Vyas',
            button_title: 'Connect now',
            button_link: 'https://www.linkedin.com/in/vyasdhanraj/',
            logo_image: {
              data: {
                id: 76,
                attributes: {
                  name: 'social_icon.svg',
                  alternativeText: null,
                  caption: null,
                  width: 45,
                  height: 46,
                  formats: null,
                  hash: 'social_icon_aa54a05c06',
                  ext: '.svg',
                  mime: 'image/svg+xml',
                  size: 0.74,
                  url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/social_icon_aa54a05c06.svg`,
                  previewUrl: null,
                  provider:
                    '@strapi-community/strapi-provider-upload-google-cloud-storage',
                  provider_metadata: null,
                  createdAt: '2024-08-13T12:56:55.089Z',
                  updatedAt: '2024-08-13T12:56:55.089Z',
                },
              },
            },
            background_image: {
              data: {
                id: 75,
                attributes: {
                  name: 'Dhanraj_Vyas_1_37058ed536.jpeg',
                  alternativeText: null,
                  caption: null,
                  width: 1627,
                  height: 2343,
                  formats: {
                    small: {
                      name: 'small_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'small_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 347,
                      height: 500,
                      size: 33.99,
                      sizeInBytes: 33987,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/small_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    thumbnail: {
                      name: 'thumbnail_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'thumbnail_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 108,
                      height: 156,
                      size: 4.69,
                      sizeInBytes: 4686,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/thumbnail_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    medium: {
                      name: 'medium_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'medium_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 521,
                      height: 750,
                      size: 68.52,
                      sizeInBytes: 68519,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/medium_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    large: {
                      name: 'large_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'large_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 694,
                      height: 1000,
                      size: 116.13,
                      sizeInBytes: 116132,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/large_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                  },
                  hash: 'Dhanraj_Vyas_1_37058ed536_2de26d2435',
                  ext: '.jpeg',
                  mime: 'image/jpeg',
                  size: 617.56,
                  url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                  previewUrl: null,
                  provider:
                    '@strapi-community/strapi-provider-upload-google-cloud-storage',
                  provider_metadata: null,
                  createdAt: '2024-08-13T12:56:31.746Z',
                  updatedAt: '2024-08-13T12:56:31.746Z',
                },
              },
            },
          },
          {
            id: 2,
            card_description: '<p>Head of Business Development</p>',
            card_title: 'Dhanraj Vyas',
            button_title: 'Connect now',
            button_link: 'https://www.linkedin.com/in/vyasdhanraj/',
            logo_image: {
              data: {
                id: 76,
                attributes: {
                  name: 'social_icon.svg',
                  alternativeText: null,
                  caption: null,
                  width: 45,
                  height: 46,
                  formats: null,
                  hash: 'social_icon_aa54a05c06',
                  ext: '.svg',
                  mime: 'image/svg+xml',
                  size: 0.74,
                  url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/social_icon_aa54a05c06.svg`,
                  previewUrl: null,
                  provider:
                    '@strapi-community/strapi-provider-upload-google-cloud-storage',
                  provider_metadata: null,
                  createdAt: '2024-08-13T12:56:55.089Z',
                  updatedAt: '2024-08-13T12:56:55.089Z',
                },
              },
            },
            background_image: {
              data: {
                id: 75,
                attributes: {
                  name: 'Dhanraj_Vyas_1_37058ed536.jpeg',
                  alternativeText: null,
                  caption: null,
                  width: 1627,
                  height: 2343,
                  formats: {
                    small: {
                      name: 'small_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'small_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 347,
                      height: 500,
                      size: 33.99,
                      sizeInBytes: 33987,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/small_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    thumbnail: {
                      name: 'thumbnail_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'thumbnail_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 108,
                      height: 156,
                      size: 4.69,
                      sizeInBytes: 4686,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/thumbnail_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    medium: {
                      name: 'medium_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'medium_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 521,
                      height: 750,
                      size: 68.52,
                      sizeInBytes: 68519,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/medium_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    large: {
                      name: 'large_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'large_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 694,
                      height: 1000,
                      size: 116.13,
                      sizeInBytes: 116132,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/large_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                  },
                  hash: 'Dhanraj_Vyas_1_37058ed536_2de26d2435',
                  ext: '.jpeg',
                  mime: 'image/jpeg',
                  size: 617.56,
                  url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                  previewUrl: null,
                  provider:
                    '@strapi-community/strapi-provider-upload-google-cloud-storage',
                  provider_metadata: null,
                  createdAt: '2024-08-13T12:56:31.746Z',
                  updatedAt: '2024-08-13T12:56:31.746Z',
                },
              },
            },
          },
          {
            id: 3,
            card_description: '<p>Head of Business Development</p>',
            card_title: 'Dhanraj Vyas',
            button_title: 'Connect now',
            button_link: 'https://www.linkedin.com/in/vyasdhanraj/',
            logo_image: {
              data: {
                id: 76,
                attributes: {
                  name: 'social_icon.svg',
                  alternativeText: null,
                  caption: null,
                  width: 45,
                  height: 46,
                  formats: null,
                  hash: 'social_icon_aa54a05c06',
                  ext: '.svg',
                  mime: 'image/svg+xml',
                  size: 0.74,
                  url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/social_icon_aa54a05c06.svg`,
                  previewUrl: null,
                  provider:
                    '@strapi-community/strapi-provider-upload-google-cloud-storage',
                  provider_metadata: null,
                  createdAt: '2024-08-13T12:56:55.089Z',
                  updatedAt: '2024-08-13T12:56:55.089Z',
                },
              },
            },
            background_image: {
              data: {
                id: 75,
                attributes: {
                  name: 'Dhanraj_Vyas_1_37058ed536.jpeg',
                  alternativeText: null,
                  caption: null,
                  width: 1627,
                  height: 2343,
                  formats: {
                    small: {
                      name: 'small_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'small_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 347,
                      height: 500,
                      size: 33.99,
                      sizeInBytes: 33987,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/small_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    thumbnail: {
                      name: 'thumbnail_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'thumbnail_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 108,
                      height: 156,
                      size: 4.69,
                      sizeInBytes: 4686,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/thumbnail_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    medium: {
                      name: 'medium_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'medium_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 521,
                      height: 750,
                      size: 68.52,
                      sizeInBytes: 68519,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/medium_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    large: {
                      name: 'large_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'large_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 694,
                      height: 1000,
                      size: 116.13,
                      sizeInBytes: 116132,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/large_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                  },
                  hash: 'Dhanraj_Vyas_1_37058ed536_2de26d2435',
                  ext: '.jpeg',
                  mime: 'image/jpeg',
                  size: 617.56,
                  url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                  previewUrl: null,
                  provider:
                    '@strapi-community/strapi-provider-upload-google-cloud-storage',
                  provider_metadata: null,
                  createdAt: '2024-08-13T12:56:31.746Z',
                  updatedAt: '2024-08-13T12:56:31.746Z',
                },
              },
            },
          },
          {
            id: 4,
            card_description: '<p>Head of Business Development</p>',
            card_title: 'Dhanraj Vyas',
            button_title: 'Connect now',
            button_link: 'https://www.linkedin.com/in/vyasdhanraj/',
            logo_image: {
              data: {
                id: 76,
                attributes: {
                  name: 'social_icon.svg',
                  alternativeText: null,
                  caption: null,
                  width: 45,
                  height: 46,
                  formats: null,
                  hash: 'social_icon_aa54a05c06',
                  ext: '.svg',
                  mime: 'image/svg+xml',
                  size: 0.74,
                  url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/social_icon_aa54a05c06.svg`,
                  previewUrl: null,
                  provider:
                    '@strapi-community/strapi-provider-upload-google-cloud-storage',
                  provider_metadata: null,
                  createdAt: '2024-08-13T12:56:55.089Z',
                  updatedAt: '2024-08-13T12:56:55.089Z',
                },
              },
            },
            background_image: {
              data: {
                id: 75,
                attributes: {
                  name: 'Dhanraj_Vyas_1_37058ed536.jpeg',
                  alternativeText: null,
                  caption: null,
                  width: 1627,
                  height: 2343,
                  formats: {
                    small: {
                      name: 'small_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'small_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 347,
                      height: 500,
                      size: 33.99,
                      sizeInBytes: 33987,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/small_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    thumbnail: {
                      name: 'thumbnail_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'thumbnail_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 108,
                      height: 156,
                      size: 4.69,
                      sizeInBytes: 4686,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/thumbnail_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    medium: {
                      name: 'medium_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'medium_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 521,
                      height: 750,
                      size: 68.52,
                      sizeInBytes: 68519,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/medium_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    large: {
                      name: 'large_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'large_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 694,
                      height: 1000,
                      size: 116.13,
                      sizeInBytes: 116132,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/large_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                  },
                  hash: 'Dhanraj_Vyas_1_37058ed536_2de26d2435',
                  ext: '.jpeg',
                  mime: 'image/jpeg',
                  size: 617.56,
                  url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                  previewUrl: null,
                  provider:
                    '@strapi-community/strapi-provider-upload-google-cloud-storage',
                  provider_metadata: null,
                  createdAt: '2024-08-13T12:56:31.746Z',
                  updatedAt: '2024-08-13T12:56:31.746Z',
                },
              },
            },
          },
          {
            id: 5,
            card_description: '<p>Head of Business Development</p>',
            card_title: 'Dhanraj Vyas',
            button_title: 'Connect now',
            button_link: 'https://www.linkedin.com/in/vyasdhanraj/',
            logo_image: {
              data: {
                id: 76,
                attributes: {
                  name: 'social_icon.svg',
                  alternativeText: null,
                  caption: null,
                  width: 45,
                  height: 46,
                  formats: null,
                  hash: 'social_icon_aa54a05c06',
                  ext: '.svg',
                  mime: 'image/svg+xml',
                  size: 0.74,
                  url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/social_icon_aa54a05c06.svg`,
                  previewUrl: null,
                  provider:
                    '@strapi-community/strapi-provider-upload-google-cloud-storage',
                  provider_metadata: null,
                  createdAt: '2024-08-13T12:56:55.089Z',
                  updatedAt: '2024-08-13T12:56:55.089Z',
                },
              },
            },
            background_image: {
              data: {
                id: 75,
                attributes: {
                  name: 'Dhanraj_Vyas_1_37058ed536.jpeg',
                  alternativeText: null,
                  caption: null,
                  width: 1627,
                  height: 2343,
                  formats: {
                    small: {
                      name: 'small_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'small_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 347,
                      height: 500,
                      size: 33.99,
                      sizeInBytes: 33987,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/small_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    thumbnail: {
                      name: 'thumbnail_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'thumbnail_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 108,
                      height: 156,
                      size: 4.69,
                      sizeInBytes: 4686,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/thumbnail_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    medium: {
                      name: 'medium_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'medium_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 521,
                      height: 750,
                      size: 68.52,
                      sizeInBytes: 68519,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/medium_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    large: {
                      name: 'large_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'large_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 694,
                      height: 1000,
                      size: 116.13,
                      sizeInBytes: 116132,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/large_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                  },
                  hash: 'Dhanraj_Vyas_1_37058ed536_2de26d2435',
                  ext: '.jpeg',
                  mime: 'image/jpeg',
                  size: 617.56,
                  url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                  previewUrl: null,
                  provider:
                    '@strapi-community/strapi-provider-upload-google-cloud-storage',
                  provider_metadata: null,
                  createdAt: '2024-08-13T12:56:31.746Z',
                  updatedAt: '2024-08-13T12:56:31.746Z',
                },
              },
            },
          },
          {
            id: 6,
            card_description: '<p>Head of Business Development</p>',
            card_title: 'Dhanraj Vyas',
            button_title: 'Connect now',
            button_link: 'https://www.linkedin.com/in/vyasdhanraj/',
            logo_image: {
              data: {
                id: 76,
                attributes: {
                  name: 'social_icon.svg',
                  alternativeText: null,
                  caption: null,
                  width: 45,
                  height: 46,
                  formats: null,
                  hash: 'social_icon_aa54a05c06',
                  ext: '.svg',
                  mime: 'image/svg+xml',
                  size: 0.74,
                  url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/social_icon_aa54a05c06.svg`,
                  previewUrl: null,
                  provider:
                    '@strapi-community/strapi-provider-upload-google-cloud-storage',
                  provider_metadata: null,
                  createdAt: '2024-08-13T12:56:55.089Z',
                  updatedAt: '2024-08-13T12:56:55.089Z',
                },
              },
            },
            background_image: {
              data: {
                id: 75,
                attributes: {
                  name: 'Dhanraj_Vyas_1_37058ed536.jpeg',
                  alternativeText: null,
                  caption: null,
                  width: 1627,
                  height: 2343,
                  formats: {
                    small: {
                      name: 'small_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'small_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 347,
                      height: 500,
                      size: 33.99,
                      sizeInBytes: 33987,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/small_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    thumbnail: {
                      name: 'thumbnail_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'thumbnail_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 108,
                      height: 156,
                      size: 4.69,
                      sizeInBytes: 4686,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/thumbnail_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    medium: {
                      name: 'medium_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'medium_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 521,
                      height: 750,
                      size: 68.52,
                      sizeInBytes: 68519,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/medium_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                    large: {
                      name: 'large_Dhanraj_Vyas_1_37058ed536.jpeg',
                      hash: 'large_Dhanraj_Vyas_1_37058ed536_2de26d2435',
                      ext: '.jpeg',
                      mime: 'image/jpeg',
                      path: null,
                      width: 694,
                      height: 1000,
                      size: 116.13,
                      sizeInBytes: 116132,
                      url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/large_Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                    },
                  },
                  hash: 'Dhanraj_Vyas_1_37058ed536_2de26d2435',
                  ext: '.jpeg',
                  mime: 'image/jpeg',
                  size: 617.56,
                  url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/Dhanraj_Vyas_1_37058ed536_2de26d2435.jpeg`,
                  previewUrl: null,
                  provider:
                    '@strapi-community/strapi-provider-upload-google-cloud-storage',
                  provider_metadata: null,
                  createdAt: '2024-08-13T12:56:31.746Z',
                  updatedAt: '2024-08-13T12:56:31.746Z',
                },
              },
            },
          },
        ],
      },
    },
  },
  meta: {},
};

export function MeetOurTeamStory() {
  return (
    <>
      <MeetOurTeam
        meetOurTeamData={data?.data?.attributes?.meet_our_team}
        variant="about-us"
      />
    </>
  );
}
