'use client';

import React from 'react';
import { Container } from 'react-bootstrap';
import styles from './PrAndNews.module.css';
import Image from 'next/image';
import useEmblaCarousel from 'embla-carousel-react';
import DotButton from '@components/DotButton/DotButton';
import Heading from '@components/Heading';
import useDotButton from '@hooks/useDotButton';
import useMediaQueryState from '@hooks/useMediaQueryState';
import breakpoints from '@styles/breakpoints.module.css';
import Link from 'next/link';
import emblastyles from '../../styles/emlaDots.module.css';
import classNames from '@utils/classNames';

export default function PrAndNews({ data, variantWhite = true }) {
  let arrayOfPrAndNewsItems = [
    ...data?.cards?.prs?.data,
    ...data?.cards?.news?.data,
  ];

  const [emblaRef, emblaApi] = useEmblaCarousel({});

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  const isTablet = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-md']})`,
  });

  return isTablet ? (
    <Container fluid className={styles.container}>
      <Heading headingType="h2" title={data?.title} className={styles.title} />
      <div className={styles.embla}>
        <div className={styles.embla__viewport} ref={emblaRef}>
          <div className={styles.embla__container}>
            {arrayOfPrAndNewsItems.map((item, index) => {
              return (
                <Link
                  href={`${item?.attributes?.slug}`}
                  prefetch={false}
                  key={index}
                  className={styles.card}
                  target="_blank"
                >
                  <div className={styles.cardPreview}>
                    <Image
                      src={
                        item?.attributes?.image?.data?.attributes?.format?.small
                          ?.url ||
                        item?.attributes?.image?.data?.attributes?.formats
                          ?.small?.url ||
                        item?.attributes?.image?.data?.attributes?.format
                          ?.medium?.url ||
                        item?.attributes?.image?.data?.attributes?.formats
                          ?.medium?.url ||
                        item?.attributes?.image?.data?.attributes?.url
                      }
                      alt={
                        item?.attributes?.image?.data?.attributes
                          ?.alternativeText
                      }
                      className={styles.previewImage}
                      width={283}
                      height={200}
                    />
                    <div className={styles.previewBottom}>
                      <div className={styles.previewBottomContent}>
                        <h3>Read</h3>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="17"
                          viewBox="0 0 16 17"
                          fill="none"
                        >
                          <path
                            d="M3.33203 8.44141H12.6654"
                            stroke="white"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M8 3.77539L12.6667 8.44206L8 13.1087"
                            stroke="white"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                  <div className={styles.cardDate}>
                    {item?.attributes?.date}
                  </div>
                  <Heading
                    headingType="h3"
                    title={item?.attributes?.title}
                    className={styles.cardTitle}
                  />
                </Link>
              );
            })}
          </div>
        </div>
        <div className={styles.embla__controls}>
          <div className={emblastyles.embla__dots}>
            {scrollSnaps.length > 1 &&
              scrollSnaps.map((_, index) => (
                <DotButton
                  key={index}
                  onClick={() => onDotButtonClick(index)}
                  className={
                    index === selectedIndex
                      ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                      : variantWhite
                        ? classNames(
                            emblastyles.embla__dot,
                            emblastyles.embla__dot_bg_white,
                          )
                        : emblastyles.embla__dot
                  }
                />
              ))}
          </div>
        </div>
      </div>
    </Container>
  ) : (
    <Container fluid className={styles.container}>
      <Heading headingType="h2" title={data?.title} className={styles.title} />
      <div className={styles.cardWrapper}>
        {arrayOfPrAndNewsItems.map((item, index) => {
          return (
            <Link
              href={`${item?.attributes?.slug}`}
              prefetch={false}
              key={index}
              className={styles.card}
              target="_blank"
            >
              <div className={styles.cardPreview}>
                <Image
                  src={
                    item?.attributes?.image?.data?.attributes?.format?.small
                      ?.url ||
                    item?.attributes?.image?.data?.attributes?.formats?.small
                      ?.url ||
                    item?.attributes?.image?.data?.attributes?.format?.medium
                      ?.url ||
                    item?.attributes?.image?.data?.attributes?.formats?.medium
                      ?.url ||
                    item?.attributes?.image?.data?.attributes?.url
                  }
                  alt={
                    item?.attributes?.image?.data?.attributes?.alternativeText
                  }
                  className={styles.previewImage}
                  width={283}
                  height={200}
                />
                <div className={styles.previewBottom}>
                  <div className={styles.previewBottomContent}>
                    <h3>Read</h3>
                    <span className={styles.arrow_styling}>
                      <Image
                        src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/arrow_right_white_9e2cd32654_2ce076c3cd.svg`}
                        width={20}
                        height={20}
                        alt={'arrow image'}
                      />
                    </span>
                    <span className={styles.arrow_styling_hover}>
                      <Image
                        src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/arrow_right_gradient_bf42e724af_d3b8061b36.svg`}
                        alt="arrow hover Image"
                        width={20}
                        height={20}
                      />
                    </span>
                  </div>
                </div>
              </div>
              <div className={styles.cardContent}>
                <div className={styles.cardDate}>{item?.attributes?.date}</div>
              </div>
              <Heading
                headingType="h3"
                title={item?.attributes?.title}
                className={styles.cardTitle}
              />
            </Link>
          );
        })}
      </div>
    </Container>
  );
}
