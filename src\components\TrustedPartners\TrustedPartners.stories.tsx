import TrustedPartners from './TrustedPartners';

export default {
  title: 'Components/TrustedPartners',
};

const data = {
  data: {
    id: 3,
    title: 'Trusted Partners',
    partnersLogo: {
      id: 3,
      images: {
        data: [
          {
            id: 17,
            attributes: {
              name: 'image 98.png',
              alternativeText: null,
              caption: null,
              width: 184,
              height: 40,
              formats: null,
              hash: 'image_98_22d1f8148c',
              ext: '.png',
              mime: 'image/png',
              size: 1.16,
              url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/image_98_22d1f8148c.png`,
              previewUrl: null,
              provider:
                '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2024-06-05T08:47:52.664Z',
              updatedAt: '2024-06-05T08:47:52.664Z',
            },
          },
          {
            id: 22,
            attributes: {
              name: 'image 98.png',
              alternativeText: null,
              caption: null,
              width: 184,
              height: 40,
              formats: null,
              hash: 'image_98_600955cfec',
              ext: '.png',
              mime: 'image/png',
              size: 1.16,
              url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/image_98_600955cfec.png`,
              previewUrl: null,
              provider:
                '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2024-06-05T08:47:53.793Z',
              updatedAt: '2024-06-05T08:47:53.793Z',
            },
          },
          {
            id: 21,
            attributes: {
              name: 'image 98.png',
              alternativeText: null,
              caption: null,
              width: 184,
              height: 40,
              formats: null,
              hash: 'image_98_441fe5b026',
              ext: '.png',
              mime: 'image/png',
              size: 1.16,
              url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/image_98_441fe5b026.png`,
              previewUrl: null,
              provider:
                '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2024-06-05T08:47:53.559Z',
              updatedAt: '2024-06-05T08:47:53.559Z',
            },
          },
          {
            id: 20,
            attributes: {
              name: 'image 98.png',
              alternativeText: null,
              caption: null,
              width: 184,
              height: 40,
              formats: null,
              hash: 'image_98_95f115efd0',
              ext: '.png',
              mime: 'image/png',
              size: 1.16,
              url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/image_98_95f115efd0.png`,
              previewUrl: null,
              provider:
                '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2024-06-05T08:47:52.710Z',
              updatedAt: '2024-06-28T10:00:42.883Z',
            },
          },
          {
            id: 19,
            attributes: {
              name: 'image 98.png',
              alternativeText: null,
              caption: null,
              width: 184,
              height: 40,
              formats: null,
              hash: 'image_98_06fe7121fb',
              ext: '.png',
              mime: 'image/png',
              size: 1.16,
              url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/image_98_06fe7121fb.png`,
              previewUrl: null,
              provider:
                '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2024-06-05T08:47:52.693Z',
              updatedAt: '2024-06-05T08:47:52.693Z',
            },
          },
          {
            id: 16,
            attributes: {
              name: 'image 98.png',
              alternativeText: null,
              caption: null,
              width: 184,
              height: 40,
              formats: null,
              hash: 'image_98_19c208863c',
              ext: '.png',
              mime: 'image/png',
              size: 1.16,
              url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/image_98_19c208863c.png`,
              previewUrl: null,
              provider:
                '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2024-06-05T08:47:52.454Z',
              updatedAt: '2024-06-05T08:47:52.454Z',
            },
          },
          {
            id: 18,
            attributes: {
              name: 'image 98.png',
              alternativeText: null,
              caption: null,
              width: 184,
              height: 40,
              formats: null,
              hash: 'image_98_203e596846',
              ext: '.png',
              mime: 'image/png',
              size: 1.16,
              url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/image_98_203e596846.png`,
              previewUrl: null,
              provider:
                '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2024-06-05T08:47:52.678Z',
              updatedAt: '2024-06-05T08:47:52.678Z',
            },
          },
          {
            id: 23,
            attributes: {
              name: 'image 98.png',
              alternativeText: null,
              caption: null,
              width: 184,
              height: 40,
              formats: null,
              hash: 'image_98_cae0a69ebd',
              ext: '.png',
              mime: 'image/png',
              size: 1.16,
              url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/image_98_cae0a69ebd.png`,
              previewUrl: null,
              provider:
                '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2024-06-05T08:47:54.027Z',
              updatedAt: '2024-06-05T08:47:54.027Z',
            },
          },
        ],
      },
    },
  },
};

export function TrustedPartnersStory() {
  return (
    <div>
      <TrustedPartners data={data.data} />
    </div>
  );
}
